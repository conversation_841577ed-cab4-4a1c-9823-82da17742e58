defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Support.{PrepareExtension, ValidateExtension, ManualExtension}

  describe "extension registration" do
    test "register_extension/1 adds new extensions to registry" do
      defmodule Test.MyOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
        register_extension(ManualExtension)
      end

      assert Drops.Operations.registered_extensions() == []

      assert Test.MyOperations.registered_extensions() == [
               PrepareExtension,
               ManualExtension
             ]
    end

    test "operations inherit extensions from base module" do
      defmodule Test.MyOperationsWithExtensions do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.MyOperation do
        use Test.MyOperationsWithExtensions

        def execute(context) do
          {:ok, context}
        end
      end

      assert Test.MyOperationsWithExtensions.registered_extensions() == [PrepareExtension]
    end
  end

  describe "PrepareExtension behavior" do
    test "adds prepared_by field to context during prepare step" do
      defmodule Test.PrepareOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.PrepareOperation do
        use Test.PrepareOperations,
          type: :command,
          extensions: [PrepareExtension],
          prepare_extension: true

        def execute(context) do
          # Return the context so we can verify the prepare step worked
          {:ok, context}
        end
      end

      # Test that the extension is loaded
      assert Test.PrepareOperation.__prepare_extension_loaded?()

      # Test that the prepare extension modifies the context
      {:ok, result} = Test.PrepareOperation.call(%{params: %{name: "test"}})

      # The result should include the prepared_by field added by the extension
      assert result.prepared_by == :prepare_extension
      assert result.params == %{name: "test"}
    end

    test "extension is not active when option is not provided" do
      defmodule Test.NoPrepareOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
      end

      defmodule Test.NoPrepareOperation do
        use Test.NoPrepareOperations, type: :command

        def execute(context) do
          {:ok, context}
        end
      end

      # Test that the extension is not loaded when option is not provided
      refute function_exported?(Test.NoPrepareOperation, :__prepare_extension_loaded?, 0)

      # Test that the prepare extension does not modify the context
      {:ok, result} = Test.NoPrepareOperation.call(%{params: %{name: "test"}})

      # The result should not include the prepared_by field
      refute Map.has_key?(result, :prepared_by)
      assert result.params == %{name: "test"}
    end
  end

  describe "ValidateExtension behavior" do
    test "validates validation_token and adds validated_by field" do
      defmodule Test.ValidateOperations do
        use Drops.Operations

        register_extension(ValidateExtension)
      end

      defmodule Test.ValidateOperation do
        use Test.ValidateOperations,
          type: :command,
          extensions: [ValidateExtension],
          validate_extension: true

        def execute(context) do
          # Return the context so we can verify the validate step worked
          {:ok, context}
        end
      end

      # Test that the extension is loaded
      assert Test.ValidateOperation.__validate_extension_loaded?()

      # Test successful validation with valid token
      {:ok, result} =
        Test.ValidateOperation.call(%{
          params: %{name: "test"},
          validation_token: "valid-token"
        })

      # The result should include the validated_by field added by the extension
      assert result.validated_by == :validate_extension
      assert result.validation_token == "valid-token"
      assert result.params == %{name: "test"}
    end

    test "fails validation when validation_token is missing" do
      defmodule Test.ValidateOperations2 do
        use Drops.Operations

        register_extension(ValidateExtension)
      end

      defmodule Test.ValidateOperation2 do
        use Test.ValidateOperations2,
          type: :command,
          extensions: [ValidateExtension],
          validate_extension: true

        def execute(context) do
          {:ok, context}
        end
      end

      # Test validation failure when token is missing
      {:error, error} = Test.ValidateOperation2.call(%{params: %{name: "test"}})

      assert error == "validation_token is required by ValidateExtension"
    end

    test "fails validation when validation_token is not a string" do
      defmodule Test.ValidateOperations3 do
        use Drops.Operations

        register_extension(ValidateExtension)
      end

      defmodule Test.ValidateOperation3 do
        use Test.ValidateOperations3, type: :command, validate_extension: true

        def execute(context) do
          {:ok, context}
        end
      end

      # Test validation failure when token is not a string
      {:error, error} =
        Test.ValidateOperation3.call(%{
          params: %{name: "test"},
          validation_token: 123
        })

      assert error == "validation_token must be a string"
    end
  end

  describe "multiple extensions working together" do
    test "both PrepareExtension and ValidateExtension can work together" do
      defmodule Test.MultiExtensionOperations do
        use Drops.Operations

        register_extension(PrepareExtension)
        register_extension(ValidateExtension)
      end

      defmodule Test.MultiExtensionOperation do
        use Test.MultiExtensionOperations,
          type: :command,
          prepare_extension: true,
          validate_extension: true

        def execute(context) do
          {:ok, context}
        end
      end

      # Test that both extensions are loaded
      assert Test.MultiExtensionOperation.__prepare_extension_loaded?()
      assert Test.MultiExtensionOperation.__validate_extension_loaded?()

      # Test that both extensions modify the context
      {:ok, result} =
        Test.MultiExtensionOperation.call(%{
          params: %{name: "test"},
          validation_token: "valid-token"
        })

      # Both extensions should have added their fields
      assert result.prepared_by == :prepare_extension
      assert result.validated_by == :validate_extension
      assert result.validation_token == "valid-token"
      assert result.params == %{name: "test"}
    end
  end
end
