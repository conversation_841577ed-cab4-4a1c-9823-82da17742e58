defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Support.{TestExtension, ManualExtension}

  describe "extension registration" do
    test "register_extension/1 adds new extensions to registry" do
      defmodule Test.MyOperations do
        use Drops.Operations

        register_extension(TestExtension)
        register_extension(ManualExtension)
      end

      assert Drops.Operations.registered_extensions() == []
      assert Test.MyOperations.registered_extensions() == [TestExtension, ManualExtension]
    end

    test "operations inherit extensions from base module" do
      defmodule Test.MyOperationsWithExtensions do
        use Drops.Operations

        register_extension(TestExtension)
      end

      defmodule Test.MyOperation do
        use Test.MyOperationsWithExtensions

        def execute(context) do
          {:ok, context}
        end
      end

      assert Test.MyOperationsWithExtensions.registered_extensions() == [TestExtension]
    end
  end
end
