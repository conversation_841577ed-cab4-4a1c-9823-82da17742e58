defmodule Test.Support.PrepareExtension do
  @moduledoc """
  A test extension that provides custom prepare functionality.

  This extension adds a custom prepare step that modifies the context
  by adding a :prepared_by field to track that it was processed.
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :prepare_extension)
  end

  @impl true
  def extend_using_macro(_opts) do
    quote do
      # Add a module attribute to track that this extension was loaded
      @prepare_extension_loaded true

      # Define a function to check if the extension is loaded
      def __prepare_extension_loaded?, do: @prepare_extension_loaded
    end
  end

  @impl true
  def extend_operation_runtime(opts) do
    if Keyword.get(opts, :prepare_extension) do
      quote do
        # Override prepare to add custom behavior
        def prepare(context) do
          # Call the original prepare first
          case super(context) do
            {:ok, updated_context} ->
              # Add our custom field to track that this extension processed the context
              prepared_context =
                Map.put(updated_context, :prepared_by, :prepare_extension)

              {:ok, prepared_context}

            error ->
              error
          end
        end

        defoverridable prepare: 1
      end
    else
      quote do
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _opts) do
    uow
  end
end

defmodule Test.Support.ValidateExtension do
  @moduledoc """
  A test extension that provides custom validate functionality.

  This extension adds a custom validate step that checks for a required
  :validation_token field and adds validation metadata.
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :validate_extension)
  end

  @impl true
  def extend_using_macro(_opts) do
    quote do
      # Add a module attribute to track that this extension was loaded
      @validate_extension_loaded true

      # Define a function to check if the extension is loaded
      def __validate_extension_loaded?, do: @validate_extension_loaded
    end
  end

  @impl true
  def extend_operation_runtime(opts) do
    if Keyword.get(opts, :validate_extension) do
      quote do
        # Override validate to add custom behavior
        def validate(context) do
          # Call the original validate first
          case super(context) do
            {:ok, validated_context} ->
              # Check for required validation_token
              case Map.get(validated_context, :validation_token) do
                nil ->
                  {:error, "validation_token is required by ValidateExtension"}

                token when is_binary(token) ->
                  # Add validation metadata
                  updated_context =
                    Map.put(validated_context, :validated_by, :validate_extension)

                  {:ok, updated_context}

                _ ->
                  {:error, "validation_token must be a string"}
              end

            error ->
              error
          end
        end

        defoverridable validate: 1
      end
    else
      quote do
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _opts) do
    uow
  end
end
